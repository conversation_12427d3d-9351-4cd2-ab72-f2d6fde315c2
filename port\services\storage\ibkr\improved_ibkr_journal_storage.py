"""
Improved IBKR Journal Storage Service with enhanced performance, error handling, and monitoring.
This service provides optimized bulk operations, comprehensive error tracking, and better observability.
"""

import datetime
import logging
import time
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Set
from django.db import transaction
from django.db.models import QuerySet
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist

from port.models import (
    Journal, Trade, CashTransaction, CorporateAction, SalesTax, TransactionTax,
    Ubo, Custodian, Partner, Account, Currency, Bnr
)
from port.services.storage.ibkr.ibkr_journal_storage import IbkrJournalStorage
from port.services.provider.deposits_service import DepositsService

logger = logging.getLogger(__name__)


@dataclass
class ImportStats:
    total_processed: int = 0
    successful: int = 0
    failed: int = 0
    skipped: int = 0
    start_time: float = field(default_factory=time.time)
    end_time: float = 0
    errors: List[str] = field(default_factory=list)
    source_breakdown: Dict[str, int] = field(default_factory=dict)
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time if self.end_time > 0 else 0
    
    @property
    def success_rate(self) -> float:
        return (self.successful / self.total_processed * 100) if self.total_processed > 0 else 0
    
    def add_source_count(self, source: str, count: int):
        """Add count for a specific source type"""
        self.source_breakdown[source] = count
        
    def to_dict(self) -> Dict:
        """Convert stats to dictionary for serialization"""
        return {
            'total_processed': self.total_processed,
            'successful': self.successful,
            'failed': self.failed,
            'skipped': self.skipped,
            'duration': self.duration,
            'success_rate': self.success_rate,
            'errors': self.errors,
            'source_breakdown': self.source_breakdown
        }


class ImprovedIbkrJournalStorage:
    def __init__(self):
        self.batch_size = getattr(settings, 'JOURNAL_IMPORT_BATCH_SIZE', 1000)
        self.transaction_id_cache: Dict[str, int] = {}
        self.current_batch_transaction_ids: Set[str] = set()
        self.account_cache: Dict[str, Account] = {}
        self.bnr_cache: Dict[tuple, float] = {}  # Cache for (currency, date) -> bnr_rate
        self._validate_base_objects()
    
    def _validate_base_objects(self):
        """Validate that all required base objects exist"""
        try:
            self.ubo = Ubo.objects.get(ubo_code='DD')
            self.custodian = Custodian.objects.get(custodian_code='IBKR')
            self.partner = Partner.objects.get(partner_code='IBKR')
            # Don't cache a single account anymore - we'll get accounts by currency
        except ObjectDoesNotExist as e:
            raise ValueError(f"Missing required base object: {e}. "
                           "Ensure UBO 'DD', Custodian 'IBKR', Partner 'IBKR' "
                           "exist in the database.")
    
    def store_journals(self, date: Optional[datetime.date] = None) -> ImportStats:
        stats = ImportStats()
        
        try:
            logger.info(f"Starting enhanced IBKR journal import for date: {date or 'all dates'}")
            
            # Clear cache for fresh import
            self.transaction_id_cache.clear()
            self.current_batch_transaction_ids.clear()
            self.account_cache.clear()
            self.bnr_cache.clear()
            
            journal_entries = self._collect_all_journal_entries(date, stats)
            
            if not journal_entries:
                logger.info("No journal entries to process")
                stats.end_time = time.time()
                return stats
            
            storage_stats = self._bulk_store_with_conflict_resolution(journal_entries)
            
            stats.total_processed = storage_stats.total_processed
            stats.successful = storage_stats.successful
            stats.failed = storage_stats.failed
            stats.skipped = storage_stats.skipped
            stats.errors.extend(storage_stats.errors)
            
            stats.end_time = time.time()
            
            self._log_import_summary(stats)
            
            return stats
            
        except Exception as e:
            stats.end_time = time.time()
            error_msg = f"Critical error in enhanced IBKR journal import: {e}"
            logger.exception(error_msg)
            stats.errors.append(error_msg)
            return stats
    
    def _collect_all_journal_entries(self, date: Optional[datetime.date], stats: ImportStats) -> List[Journal]:
        journal_entries = []

        # Define source processors
        sources = [
            ('commissions', lambda: self._get_commission_journals(date)),
            ('fx_trades', lambda: self._get_fx_journals(date)),
            ('asset_trades', lambda: self._get_asset_trade_journals(date)),
            ('cash_transactions', lambda: self._get_cash_journals(date)),
            ('corporate_actions', lambda: self._get_corporate_action_journals(date)),
            ('sales_taxes', lambda: self._get_sales_tax_journals(date)),
            ('transaction_taxes', lambda: self._get_transaction_tax_journals(date)),
            ('profits', lambda: self._get_profit_journals(date)),
            # ('bond_accruals', lambda: self._get_bond_accrual_journals())
        ]

        for source_name, processor in sources:
            try:
                logger.info(f"Processing {source_name}...")
                source_journals = processor()
                journal_entries.extend(source_journals)
                stats.add_source_count(source_name, len(source_journals))
                logger.info(f"Generated {len(source_journals)} {source_name} journal entries")
            except Exception as e:
                error_msg = f"Error processing {source_name}: {e}"
                logger.error(error_msg, exc_info=True)
                stats.errors.append(error_msg)

        # Deduplicate and assign unique transaction IDs
        journal_entries = self._deduplicate_and_assign_unique_transaction_ids(journal_entries)

        # Enhance journal entries with correct accounts, BNR rates, and value_ron
        journal_entries = self._enhance_journal_entries_with_currency_data(journal_entries)

        logger.info(f"Total journal entries collected: {len(journal_entries)}")
        return journal_entries

    def _deduplicate_and_assign_unique_transaction_ids(self, journal_entries: List[Journal]) -> List[Journal]:
        if not journal_entries:
            return journal_entries

        # Step 1: Group entries by their business content (excluding transaction ID)
        business_content_groups = {}
        for entry in journal_entries:
            business_key = (
                entry.ubo.id if entry.ubo else None,
                entry.custodian.id if entry.custodian else None,
                entry.account.id if entry.account else None,
                entry.operation.id if entry.operation else None,
                entry.partner.id if entry.partner else None,
                entry.instrument.id if entry.instrument else None,
                entry.date,
                float(entry.value) if entry.value else None,
                float(entry.quantity) if entry.quantity else None,
                entry.details,
            )

            if business_key not in business_content_groups:
                business_content_groups[business_key] = []
            business_content_groups[business_key].append(entry)

        deduplicated_entries = []
        for business_key, entries in business_content_groups.items():
            if len(entries) == 1:
                entry = entries[0]
                entry.transactionid = self._get_unique_transaction_id(entry.transactionid)
                deduplicated_entries.append(entry)
            else:
                full_content_groups = {}
                for entry in entries:
                    full_key = (
                        entry.ubo.id if entry.ubo else None,
                        entry.custodian.id if entry.custodian else None,
                        entry.account.id if entry.account else None,
                        entry.operation.id if entry.operation else None,
                        entry.partner.id if entry.partner else None,
                        entry.instrument.id if entry.instrument else None,
                        entry.date,
                        float(entry.value) if entry.value else None,
                        float(entry.quantity) if entry.quantity else None,
                        entry.details,
                        entry.transactionid,
                    )

                    if full_key not in full_content_groups:
                        full_content_groups[full_key] = []
                    full_content_groups[full_key].append(entry)

                for full_key, full_entries in full_content_groups.items():
                    if len(full_entries) == 1:
                        entry = full_entries[0]
                        entry.transactionid = self._get_unique_transaction_id(entry.transactionid)
                        deduplicated_entries.append(entry)
                    else:
                        logger.warning(f"Found {len(full_entries)} exact duplicate journal entries. "
                                     f"Keeping only the first entry with transaction ID: {full_entries[0].transactionid}")
                        entry = full_entries[0]
                        entry.transactionid = self._get_unique_transaction_id(entry.transactionid)
                        deduplicated_entries.append(entry)

        logger.info(f"Deduplication complete: {len(journal_entries)} -> {len(deduplicated_entries)} entries")
        return deduplicated_entries

    def _enhance_journal_entries_with_currency_data(self, journal_entries: List[Journal]) -> List[Journal]:
        """
        Enhance journal entries with correct account based on currency, BNR rates, and value_ron calculations.
        """
        if not journal_entries:
            return journal_entries

        enhanced_entries = []
        for entry in journal_entries:
            try:
                # Determine currency from the instrument symbol or other source
                currency_code = self._extract_currency_from_entry(entry)

                # Set the correct account based on currency
                entry.account = self._get_account_by_currency(currency_code)

                # Get BNR rate for the date and currency
                bnr_rate = self._get_bnr_rate(currency_code, entry.date)
                entry.bnr = bnr_rate

                # Calculate value_ron
                entry.value_ron = float(entry.value) * bnr_rate

                enhanced_entries.append(entry)

            except Exception as e:
                logger.error(f"Error enhancing journal entry {entry.transactionid}: {e}", exc_info=True)
                # Keep the original entry if enhancement fails
                enhanced_entries.append(entry)

        logger.info(f"Enhanced {len(enhanced_entries)} journal entries with currency data")
        return enhanced_entries

    def _extract_currency_from_entry(self, entry: Journal) -> str:
        """
        Extract currency code from journal entry.
        Primary source: entry.instrument.currency.currency_code
        Fallback: lookup original transaction data
        """
        try:
            if entry.instrument and hasattr(entry.instrument, 'currency') and entry.instrument.currency:
                currency_code = entry.instrument.currency.currency_code
                logger.debug(f"Found currency {currency_code} from instrument for {entry.transactionid}")
                return currency_code

            # Remove any suffix (handle both * and .* patterns)
            import re
            transaction_id = re.sub(r'(\.\*|\*)\d+$', '', entry.transactionid)

            try:
                cash_transaction = CashTransaction.objects.get(transaction_id=transaction_id)
                currency_code = cash_transaction.currency.currency_code
                logger.debug(f"Found currency {currency_code} from CashTransaction for {transaction_id}")
                return currency_code
            except CashTransaction.DoesNotExist:
                pass

            try:
                trade = Trade.objects.get(transaction_id=transaction_id)
                currency_code = trade.currency.currency_code
                logger.debug(f"Found currency {currency_code} from Trade for {transaction_id}")
                return currency_code
            except Trade.DoesNotExist:
                pass

            try:
                corporate_action = CorporateAction.objects.get(transaction_id=transaction_id)
                currency_code = corporate_action.currency.currency_code
                logger.debug(f"Found currency {currency_code} from CorporateAction for {transaction_id}")
                return currency_code
            except CorporateAction.DoesNotExist:
                pass

            details_upper = entry.details.upper()
            if details_upper.startswith('GBP ') or ' GBP ' in details_upper:
                logger.debug(f"Extracted GBP from details for {transaction_id}: {entry.details}")
                return 'GBP'
            elif details_upper.startswith('EUR ') or ' EUR ' in details_upper:
                logger.debug(f"Extracted EUR from details for {transaction_id}: {entry.details}")
                return 'EUR'
            elif details_upper.startswith('USD ') or ' USD ' in details_upper:
                logger.debug(f"Extracted USD from details for {transaction_id}: {entry.details}")
                return 'USD'
            elif details_upper.startswith('RON ') or ' RON ' in details_upper:
                logger.debug(f"Extracted RON from details for {transaction_id}: {entry.details}")
                return 'RON'

            # Default fallback
            logger.warning(f"Could not determine currency for transaction {entry.transactionid}, defaulting to USD. Details: {entry.details}")
            return 'USD'

        except Exception as e:
            logger.error(f"Error extracting currency from entry {entry.transactionid}: {e}", exc_info=True)
            return 'USD'  # Default fallback

    def _get_unique_transaction_id(self, transaction_id: str) -> str:
        """
        Generate a unique transaction ID, ensuring no conflicts with existing database entries
        or entries in the current batch. Uses the same pattern as original uptrz_ibkr.py code.
        """
        import re

        base_id = transaction_id
        original_base_id = base_id

        # Remove existing suffix pattern (.*number) to get base ID
        base_id = re.sub(r'\.\*\d+$', '', base_id)

        if transaction_id in self.current_batch_transaction_ids:
            # Find next available suffix
            suffix = 1
            while f"{base_id}.*{suffix}" in self.current_batch_transaction_ids:
                suffix += 1
            new_transaction_id = f"{base_id}.*{suffix}"
        else:
            # Check database for existing IDs with this base (using .*pattern like original)
            similar_ids = Journal.objects.filter(
                transactionid__startswith=base_id
            ).values_list('transactionid', flat=True)

            suffixes = []
            for tid in similar_ids:
                # Match pattern: base_id or base_id.*number
                match = re.match(rf'^{re.escape(base_id)}(?:\.\*(\d+))?$', tid)
                if match:
                    suffix = match.group(1)
                    suffixes.append(int(suffix) if suffix else 0)

            # Also check current batch for conflicts
            for batch_id in self.current_batch_transaction_ids:
                if batch_id.startswith(base_id):
                    match = re.match(rf'^{re.escape(base_id)}(?:\.\*(\d+))?$', batch_id)
                    if match:
                        suffix = match.group(1)
                        suffixes.append(int(suffix) if suffix else 0)

            next_suffix = max(suffixes) + 1 if suffixes else 0
            new_transaction_id = base_id if next_suffix == 0 else f"{base_id}.*{next_suffix}"

        # Add to current batch tracking
        self.current_batch_transaction_ids.add(new_transaction_id)

        if new_transaction_id != original_base_id:
            logger.debug(f"Transaction ID '{original_base_id}' adjusted to '{new_transaction_id}' to ensure uniqueness.")

        return new_transaction_id

    def _get_account_by_currency(self, currency_code: str) -> Account:
        """
        Get the appropriate IBKR account based on currency code.
        Returns IBKR_USD, IBKR_GBP, IBKR_EUR, etc.
        """
        if currency_code in self.account_cache:
            return self.account_cache[currency_code]

        account_code = f"IBKR_{currency_code}"
        try:
            account = Account.objects.get(account_code=account_code)
            self.account_cache[currency_code] = account
            return account
        except ObjectDoesNotExist:
            # Fallback to USD account if specific currency account doesn't exist
            logger.warning(f"Account {account_code} not found, falling back to IBKR_USD")
            if 'USD' not in self.account_cache:
                self.account_cache['USD'] = Account.objects.get(account_code='IBKR_USD')
            return self.account_cache['USD']

    def _get_bnr_rate(self, currency_code: str, date: datetime.date) -> float:
        """
        Get BNR exchange rate for a specific currency and date.
        Returns 1.0 for RON, actual rate for other currencies.
        """
        if currency_code == 'RON':
            return 1.0

        cache_key = (currency_code, date)
        if cache_key in self.bnr_cache:
            return self.bnr_cache[cache_key]

        try:
            # Try to get the exact date first
            currency_obj = Currency.objects.get(currency_code=currency_code)
            bnr_entry = Bnr.objects.get(currency_code=currency_obj, date=date)
            rate = bnr_entry.value_exact if bnr_entry.value_exact else bnr_entry.value
            self.bnr_cache[cache_key] = rate
            return rate
        except (ObjectDoesNotExist, Currency.DoesNotExist):
            # Try to find the closest previous date
            try:
                currency_obj = Currency.objects.get(currency_code=currency_code)
                bnr_entry = Bnr.objects.filter(
                    currency_code=currency_obj,
                    date__lte=date
                ).order_by('-date').first()

                if bnr_entry:
                    rate = bnr_entry.value_exact if bnr_entry.value_exact else bnr_entry.value
                    self.bnr_cache[cache_key] = rate
                    logger.debug(f"Using BNR rate {rate} for {currency_code} on {bnr_entry.date} (requested {date})")
                    return rate
                else:
                    logger.warning(f"No BNR rate found for {currency_code} on or before {date}, using 1.0")
                    self.bnr_cache[cache_key] = 1.0
                    return 1.0
            except Exception as e:
                logger.error(f"Error getting BNR rate for {currency_code} on {date}: {e}")
                self.bnr_cache[cache_key] = 1.0
                return 1.0

    def _bulk_store_with_conflict_resolution(self, journal_entries: List[Journal]) -> ImportStats:
        stats = ImportStats(total_processed=len(journal_entries))
        failed_batches = []

        try:
            # First, try bulk processing in atomic transactions
            with transaction.atomic():
                for i in range(0, len(journal_entries), self.batch_size):
                    batch = journal_entries[i:i + self.batch_size]
                    batch_stats = self._process_journal_batch(batch)

                    if batch_stats.failed > 0:
                        # If batch failed, save it for individual processing
                        failed_batches.append(batch)
                        logger.info(f"Batch {i//self.batch_size + 1}: failed, will retry individually")
                    else:
                        stats.successful += batch_stats.successful
                        stats.skipped += batch_stats.skipped
                        logger.info(f"Batch {i//self.batch_size + 1}: "
                                  f"{batch_stats.successful} successful, "
                                  f"{batch_stats.skipped} skipped")

                    stats.errors.extend(batch_stats.errors)

            # Process failed batches individually outside of atomic transaction
            if failed_batches:
                logger.info(f"Processing {len(failed_batches)} failed batches individually...")
                for batch_num, batch in enumerate(failed_batches):
                    individual_stats = self._process_batch_individually(batch)
                    stats.successful += individual_stats.successful
                    stats.failed += individual_stats.failed
                    stats.skipped += individual_stats.skipped
                    stats.errors.extend(individual_stats.errors)

                    logger.info(f"Individual batch {batch_num + 1}: "
                              f"{individual_stats.successful} successful, "
                              f"{individual_stats.failed} failed, "
                              f"{individual_stats.skipped} skipped")

            return stats

        except Exception as e:
            error_msg = f"Critical error in bulk storage: {e}"
            logger.error(error_msg, exc_info=True)
            stats.errors.append(error_msg)
            stats.failed = len(journal_entries)
            return stats
    
    def _process_journal_batch(self, batch: List[Journal]) -> ImportStats:
        batch_stats = ImportStats(total_processed=len(batch))

        # Use the same logic as the original uptrz_ibkr.py code
        # Original unique fields: ['ubo', 'custodian', 'account', 'transactionid', 'operation']
        # This allows multiple entries with same transaction ID but different operations

        # Try bulk_create first (like original code)
        try:
            unique_fields = ['ubo', 'custodian', 'account', 'transactionid', 'operation']
            update_fields = unique_fields + ['partner', 'instrument', 'date', 'value', 'value_ron', 'bnr', 'quantity', 'details']

            Journal.objects.bulk_create(
                batch,
                update_conflicts=True,
                unique_fields=unique_fields,
                update_fields=update_fields,
            )

            # All entries processed successfully with bulk_create
            batch_stats.successful = len(batch)
            logger.debug(f"Bulk created/updated {len(batch)} journal entries")
            return batch_stats

        except Exception as e:
            # If bulk_create fails, we need to handle it outside the current transaction
            logger.warning(f"Bulk create failed: {e}. Will process individually outside transaction.")
            batch_stats.errors.append(f"Bulk create failed: {e}")
            # Return the error stats - the caller will handle individual processing
            batch_stats.failed = len(batch)
            return batch_stats

    def _process_batch_individually(self, batch: List[Journal]) -> ImportStats:
        """Process a batch of journal entries individually (outside atomic transaction)"""
        batch_stats = ImportStats(total_processed=len(batch))

        for journal in batch:
            try:
                # Use update_or_create to handle conflicts properly
                _, created = Journal.objects.update_or_create(
                    ubo=journal.ubo,
                    custodian=journal.custodian,
                    account=journal.account,
                    transactionid=journal.transactionid,
                    operation=journal.operation,
                    defaults={
                        'partner': journal.partner,
                        'instrument': journal.instrument,
                        'date': journal.date,
                        'value': journal.value,
                        'value_ron': journal.value_ron,
                        'bnr': journal.bnr,
                        'quantity': journal.quantity,
                        'details': journal.details,
                        'storno': journal.storno,
                        'lock': journal.lock,
                    }
                )

                if created:
                    batch_stats.successful += 1
                    logger.debug(f"Created journal entry: {journal.transactionid} - {journal.operation}")
                else:
                    batch_stats.skipped += 1
                    logger.debug(f"Updated existing journal entry: {journal.transactionid} - {journal.operation}")

            except Exception as e:
                batch_stats.failed += 1
                batch_stats.errors.append(f"Failed to process {journal.transactionid} - {journal.operation}: {e}")
                logger.error(f"Error processing journal {journal.transactionid} - {journal.operation}: {e}")

        return batch_stats

    def _calculate_deposit_accruals(self, stats: ImportStats):
        """Calculate deposit accruals with error handling"""
        try:
            logger.info("Calculating and storing deposit accruals...")
            DepositsService().calculate_and_store_accruals()
            logger.info("Deposit accruals calculation completed successfully")
        except Exception as e:
            error_msg = f"Error calculating deposit accruals: {e}"
            logger.error(error_msg, exc_info=True)
            stats.errors.append(error_msg)

    def _log_import_summary(self, stats: ImportStats):
        logger.info(f"Enhanced IBKR journal import completed:")
        logger.info(f"  Total processed: {stats.total_processed}")
        logger.info(f"  Successful: {stats.successful}")
        logger.info(f"  Failed: {stats.failed}")
        logger.info(f"  Skipped: {stats.skipped}")
        logger.info(f"  Duration: {stats.duration:.2f}s")
        logger.info(f"  Success rate: {stats.success_rate:.1f}%")

        if stats.source_breakdown:
            logger.info("Source breakdown:")
            for source, count in stats.source_breakdown.items():
                logger.info(f"  {source}: {count}")

        if stats.errors:
            logger.warning(f"Import completed with {len(stats.errors)} errors:")
            for error in stats.errors[:5]:  # Log first 5 errors
                logger.warning(f"  - {error}")
            if len(stats.errors) > 5:
                logger.warning(f"  ... and {len(stats.errors) - 5} more errors")

    def _get_commission_journals(self, date: Optional[datetime.date]) -> List[Journal]:
        """Get commission journal entries"""
        trades = Trade.objects.filter(date=date).exclude(ibcommission=0) if date else Trade.objects.exclude(ibcommission=0)
        return IbkrJournalStorage.get_commissions_from_trades(trades)

    def _get_fx_journals(self, date: Optional[datetime.date]) -> List[Journal]:
        """Get FX journal entries"""
        trades = Trade.objects.filter(date=date, assetcategory='CASH') if date else Trade.objects.filter(assetcategory='CASH')
        return IbkrJournalStorage.get_fx_from_trades(trades)

    def _get_asset_trade_journals(self, date: Optional[datetime.date]) -> List[Journal]:
        """Get asset trade journal entries"""
        trades = Trade.objects.filter(date=date).exclude(assetcategory='CASH') if date else Trade.objects.exclude(assetcategory='CASH')
        return IbkrJournalStorage.get_trades_from_trades(trades)

    def _get_cash_journals(self, date: Optional[datetime.date]) -> List[Journal]:
        """Get cash transaction journal entries"""
        cash_transactions = CashTransaction.objects.filter(date=date) if date else CashTransaction.objects.all()
        return IbkrJournalStorage.get_cash_from_cash_transactions(cash_transactions)

    def _get_corporate_action_journals(self, date: Optional[datetime.date]) -> List[Journal]:
        """Get corporate action journal entries"""
        corporate_actions = CorporateAction.objects.filter(date=date) if date else CorporateAction.objects.all()
        return IbkrJournalStorage.get_corporate_actions_from_corporate_actions(corporate_actions)

    def _get_sales_tax_journals(self, date: Optional[datetime.date]) -> List[Journal]:
        """Get sales tax journal entries"""
        sales_taxes = SalesTax.objects.filter(date=date) if date else SalesTax.objects.all()
        return IbkrJournalStorage.get_sales_taxes_from_sales_taxes(sales_taxes)

    def _get_transaction_tax_journals(self, date: Optional[datetime.date]) -> List[Journal]:
        """Get transaction tax journal entries"""
        transaction_taxes = TransactionTax.objects.filter(date=date) if date else TransactionTax.objects.all()
        return IbkrJournalStorage.get_transaction_taxes_from_transaction_taxes(transaction_taxes)

    def _get_profit_journals(self, date: Optional[datetime.date]) -> List[Journal]:
        """Get profit journal entries"""
        trades = Trade.objects.filter(date=date) if date else Trade.objects.all()
        return IbkrJournalStorage.get_profits_from_trades(trades)

    def _get_bond_accrual_journals(self) -> List[Journal]:
        """Get bond accrual journal entries"""
        return IbkrJournalStorage.get_bond_accruals_for_journal()

    def get_import_statistics(self) -> Dict:
        """Get current import statistics for monitoring"""
        return {
            'cache_size': len(self.transaction_id_cache),
            'batch_size': self.batch_size,
            'base_objects_valid': all([
                hasattr(self, 'ubo'), hasattr(self, 'custodian'),
                hasattr(self, 'partner'), hasattr(self, 'account')
            ])
        }

    def clear_cache(self):
        """Clear all caches for memory management"""
        self.transaction_id_cache.clear()
        self.current_batch_transaction_ids.clear()
        self.account_cache.clear()
        self.bnr_cache.clear()
        logger.debug("All caches cleared")
