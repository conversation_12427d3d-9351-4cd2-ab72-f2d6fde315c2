from datetime import datetime
import logging
import re
from typing import Optional

from django.db import IntegrityError, transaction

from port.models import Account, Accounting, Activitate, BondAccrual, Currency, Custodian, Instrument, Journal, Operation, Partner, Partner_type, Portof, Ubo
from port.services.storage.ibkr.ibkr_journal_storage import IbkrJournalStorage

logger = logging.getLogger(__name__)


class TDVJournalStorageService:
    @staticmethod
    def store_journals(date: Optional[datetime.date] = None) -> None:
        if date:
            activities = Activitate.objects.filter(date=date)
        else:
            activities = Activitate.objects.all()
        
        for activity in activities:
            operation = TDVJournalStorageService.get_journal_operation(activity)
            account = TDVJournalStorageService.get_account(activity)
            details = TDVJournalStorageService.get_details(activity)
            partner = TDVJournalStorageService.get_partner(activity)
            instrument = TDVJournalStorageService.get_instrument(activity)
            price, quantity, dirty = TDVJournalStorageService.get_activity_price_quantity_dirty(activity)
            value = price * quantity

            if TDVJournalStorageService.journal_exists(activity, operation, account, partner, instrument, value, quantity):
                continue
            if dirty != 0:
                interest_value = TDVJournalStorageService.get_interest_value(activity)
                interest_quantity, interest_unit_cost = 0.0, 0.0
                interest_operation = TDVJournalStorageService.get_interest_operation(activity)
                TDVJournalStorageService.add_journal_entry(
                    activity,
                    interest_operation,
                    account,
                    partner,
                    instrument,
                    interest_value,
                    interest_quantity,
                    details,
                )
            
            if activity.comis != 0:
                comission_value = activity.comis * (-1)
                comission_quantity, comission_unit_cost = 0.0, 0.0
                comission_operation = TDVJournalStorageService.get_or_create_operation('COMIS_BROKER_VALUTA')
                TDVJournalStorageService.add_journal_entry(
                    activity,
                    comission_operation,
                    account,
                    partner,
                    instrument,
                    comission_value,
                    comission_quantity,
                    details,
                )
            
            if activity.txcnlei != 0:
                tax_value = activity.txcnlei * (-1)
                tax_quantity, tax_unit_cost = 0.0, 0.0
                tax_operation = TDVJournalStorageService.get_or_create_operation('COMIS_BROKER_VALUTA')
                TDVJournalStorageService.add_journal_entry(
                    activity,
                    tax_operation,
                    account,
                    partner,
                    instrument,
                    tax_value,
                    tax_quantity,
                    details,
                )
            
            quantity = TDVJournalStorageService.get_quantity_by_symbol(activity)
            TDVJournalStorageService.add_journal_entry(
                activity,
                operation,
                account,
                partner,
                instrument,
                value,
                quantity,
                details,
            )
            tdv_bonds = BondAccrual.objects.filter(custodian__custodian_code='TDV')
            for bond in tdv_bonds:
                TDVJournalStorageService.add_journal_entry_for_bond(bond,)

    @staticmethod
    def get_activity_price_quantity_dirty(activity: Activitate) -> tuple[float, float, float]:
        if activity.operation in ['cump', 'out']:
            price = activity.price * (-1)
            dirty = activity.dirty * (-1)
            if activity.operation == "out":
                quantity = activity.quantity * (-1)
            else:
                quantity = activity.quantity
        else:
            price = activity.price
            dirty = activity.dirty
            quantity = activity.quantity
        return price, quantity, dirty

    @staticmethod
    def get_interest_value(activity: Activitate) -> float:
        if activity.dirty != 0:
            return activity.quantity * (activity.dirty - activity.price)
        return 0.0
    
    @staticmethod
    def get_interest_operation(activity: Activitate) -> str:
        if activity.dirty != 0:
            if activity.operation in ['cump', 'out']:
                return 'BOND_COUPON_PAID_BROKER'
            else:
                return 'BOND_COUPON_RECEIVED_BROKER'
        return activity.operation

    @staticmethod
    def get_partner(activity: Activitate) -> Partner:
        none_partner_type = Partner_type.objects.get_or_create(partner_type_code='NO_VALUE', defaults={'journal_code': 'NO_VALUE'})[0]
        none_partner = Partner.objects.get_or_create(partner_code='NEDEFINIT', partner_type=none_partner_type)[0]
        try:
            if activity.operation in ['in', 'out'] and (activity.details.startswith('DEPUNERE') or activity.details.startswith('RETRAGERE')):
                return none_partner
            return Partner.objects.get(partner_code='TDV')
        except Partner.DoesNotExist:
            return none_partner

    @staticmethod
    def get_quantity_by_symbol(activity: Activitate) -> float:
        CURRENCIES = ['EUR', 'USD', 'RON', 'GBP', 'CAD', 'SEK',]
        if activity.symbol in CURRENCIES:
            return 0.0
        return activity.quantity

    @staticmethod
    def get_instrument(activity: Activitate) -> Instrument:
        instrument = Instrument.objects.filter(symbol=activity.symbol, custodian__custodian_code='TDV').first()

        if instrument:
            return instrument

        logger.warning(f"Instrument not found for {activity.symbol}, creating one.")

        try:
            with transaction.atomic():
                return Instrument.objects.get_or_create(
                    symbol=activity.symbol,
                    name=activity.symbol,
                    isin=activity.symbol,
                    custodian=Custodian.objects.get(custodian_code='TDV'),
                    currency=Currency.objects.get(currency_code=activity.currency),
                    type='UNKNOWN',
                    sector='UNKNOWN',
                    country='UNKNOWN',
                )[0]
        except IntegrityError:
            # fallback in case of race condition
            return Instrument.objects.get(
                symbol=activity.symbol, custodian__custodian_code='TDV'
            )

    @staticmethod
    def get_journal_operation(activity: Activitate) -> Operation:
        fx_in = ['TRANSFER DIN D6DN39.UE IN D6DN39.US']
        fx_out = 'USD IN US CURS'        
        re_eur = ['TRANSFER DIN D6DN39-RE IN D6DN39.UE', 'TRANSFER DIN RE IN UE']
        operation = activity.operation
        if activity.operation in ['in', 'out'] and activity.details in re_eur:
            operation = 'TRANSFER_INTERN_BROKER'
        if activity.operation == 'in' and activity.details.startswith('DEPUNERE'):
            operation = 'VIR_INT_IN_BROKER_VALUTA'
        if activity.operation == 'out' and activity.details.startswith('RETRAGERE'):
            operation = 'VIR_INT_OUT_BROKER_VALUTA'
        if activity.operation == 'in' and activity.details == fx_in:
            operation = 'FX_IN'
        if activity.operation == 'out' and activity.details == fx_out:
            operation = 'FX_OUT'
        if activity.operation == 'cump':
            operation = 'BUY_BOND_BROKER'
        elif activity.operation == 'vanz':
            operation = 'SELL_BOND_BROKER'
        elif activity.operation == 'div':
            operation = 'BOND_INTEREST_RECEIVED_BROKER'
        return TDVJournalStorageService.get_or_create_operation(operation)

    @staticmethod
    def get_or_create_operation(operation_code: str) -> Operation:
        try:
            return Operation.objects.get(operation_code=operation_code)
        except Operation.DoesNotExist:
            logger.error(f"Operation not found for {operation_code}")
            default_accounting, _ = Accounting.objects.get_or_create(
                account_code='000',
                defaults={
                    'account_name': 'DEFAULT_NO_VALUE',
                    'has_currency': False,
                    'has_custodian_debit': True,
                    'has_custodian_credit': True,
                    'has_partner_debit': True,
                    'has_partner_credit': True,
                    'has_symbol': False,
                    'has_dot': False,
                }
            )

            return Operation.objects.get_or_create(
                operation_code=operation_code,
                defaults={
                    'operation_name': operation_code,
                    'debit': default_accounting,
                    'credit': default_accounting,
                }
            )[0]

    @staticmethod
    def get_account(activity: Activitate) -> Account:
        try:
            if activity.currency == 'EUR' and not activity.details:
                return Account.objects.get(account_code='TDV_RE')
            return Account.objects.get(account_code=f"TDV_{activity.currency}")
        except Account.DoesNotExist:
            logger.error(f"Account not found for {activity.currency}")
            return Account.objects.get_or_create(
                account_code='TDV_NO_VAL',
                account_name='TDV_NO_VAL',
                currency=Currency.objects.get(currency_code='EUR'),
                custodian=Custodian.objects.get(custodian_code='TDV'),
                ubo=Ubo.objects.get(ubo_code='DD'),
            )[0]
    
    
    @staticmethod
    def get_details(activity: Activitate) -> str:
        if not activity.details:
            return ''
        return ' '.join(activity.details.upper().split())

    @staticmethod
    def check_transaction_id_in_journal_and_increment_id(activity: Activitate) -> str:
        base_id = str(activity.transactionid)

        # Use .*pattern to match original uptrz_ibkr.py behavior
        base_id = re.sub(r'\.\*\d+$', '', base_id)
        similar_ids = Journal.objects.filter(transactionid__startswith=base_id).values_list('transactionid', flat=True)
        if similar_ids:
            suffixes = []
            for tid in similar_ids:
                # Updated regex to match .*pattern
                match = re.match(rf'^{re.escape(base_id)}(?:\.\*(\d+))?$', tid)
                if match:
                    suffix = match.group(1)
                    suffixes.append(int(suffix) if suffix else 0)

            next_suffix = max(suffixes, default=0) + 1 if suffixes else 0

            # Use .*pattern like original code
            activity.transactionid = base_id if next_suffix == 0 else f"{base_id}.*{next_suffix}"
        return activity.transactionid

    @staticmethod
    def journal_exists(activity, operation, account, partner, instrument, value, quantity):
        return Journal.objects.filter(
            ubo__ubo_code=activity.ubo,
            custodian__custodian_code='TDV',
            account=account,
            operation=operation,
            partner=partner,
            instrument=instrument,
            date=activity.date,
        ).exists()

    @staticmethod
    def add_journal_entry(
        activity: Activitate,
        operation: Operation,
        account: Account,
        partner: Partner,
        instrument: Instrument,
        value: float,
        quantity: float,
        details: str,
    ) -> Journal:
        try:
            Journal.objects.get_or_create(
                ubo=Ubo.objects.get(ubo_code=activity.ubo),
                custodian=Custodian.objects.get(custodian_code='TDV'),
                account=account,
                operation=operation,
                partner=partner,
                instrument=instrument,
                date=activity.date,
                transactionid=TDVJournalStorageService.check_transaction_id_in_journal_and_increment_id(activity),
                defaults={
                    "value": value,
                    "quantity": quantity,
                    "details": details,
                }
            )
        except Exception as e:
            logger.error(f"Error adding journal entry for {activity.transactionid}: {str(e)}")
            return None

    @staticmethod
    def add_journal_entry_for_bond(
        bond: BondAccrual,
    ) -> Journal:
        try:
            transaction_id = f"{bond.instrument.symbol} {bond.operation.operation_name} {bond.date}"
            Journal.objects.get_or_create(
                ubo=bond.ubo,
                custodian=bond.custodian,
                account=bond.account,
                operation=bond.operation,
                partner=bond.partner,
                instrument=bond.instrument,
                date=bond.date,
                transactionid=IbkrJournalStorage.check_transaction_id_in_journal_and_increment_id(transaction_id),
                defaults={
                    "value": bond.value,
                    "quantity": bond.quantity,
                    "details": bond.details,
                }
            )
        except Exception as e:
            logger.error(f"Error adding journal entry for {transaction_id}: {str(e)}")
